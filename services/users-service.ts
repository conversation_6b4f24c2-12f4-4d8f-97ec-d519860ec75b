import { db } from "@/lib/firebase";
import { UserRole } from "@/types/team";
import {
  collection,
  getDocs,
  query,
  doc,
  getDoc,
  setDoc,
  serverTimestamp,
} from "firebase/firestore";

/**
 * Interface para dados de registo de utilizador
 */
export interface UserRegisterData {
  email: string;
  password: string;
  fullName: string;
  registrationNumber: string;
  category: string;
  role?: UserRole;
  teamId?: string;
  canEditProfile?: boolean;
}

/**
 * Obtém todos os usuários autenticados no sistema
 * @param teamId - ID da equipa para filtrar os usuários (opcional)
 */
export async function getSystemUsers(teamId?: string): Promise<{ id: string, name: string, email?: string, registrationNumber?: string, category?: string, role?: UserRole, teamId?: string, canEditProfile?: boolean }[]> {
  try {
    // Buscar apenas utilizadores autenticados com perfil completo
    const q = query(collection(db, "users"));
    const querySnapshot = await getDocs(q);

    // Filtrar apenas utilizadores com perfil completo e da equipa especificada (se fornecida)
    const users = querySnapshot.docs
      .filter(doc => {
        const data = doc.data();
        // Garantir que só incluimos utilizadores com perfil completo
        const hasProfile = data.profileCompleted === true;

        // Se teamId for fornecido, filtrar por equipa
        if (teamId && hasProfile) {
          return data.teamId === teamId;
        }

        return hasProfile;
      })
      .map(doc => {
        const data = doc.data();
        const registrationNumber = data.registrationNumber || "";
        const fullName = data.fullName || "Utilizador sem nome";

        // Log para depuração
        console.log(`Carregando usuário ${doc.id}:`, {
          fullName,
          role: data.role,
          teamId: data.teamId
        });

        return {
          id: doc.id, // Este é o ID do utilizador autenticado (uid do Firebase Auth)
          name: fullName,
          fullName: fullName, // Para ordenação
          email: data.email,
          registrationNumber: registrationNumber,
          category: data.category,
          role: data.role,
          teamId: data.teamId,
          canEditProfile: data.canEditProfile,
        };
      })
      // Ordenar por nome
      .sort((a, b) => a.fullName.localeCompare(b.fullName));

    console.log(`Encontrados ${users.length} utilizadores autenticados com perfil completo`);
    return users;
  } catch (error) {
    console.error("Erro ao obter usuários:", error);
    throw error;
  }
}

/**
 * Obtém um usuário pelo ID
 */
export async function getUser(userId: string): Promise<{ id: string, name: string, email?: string, registrationNumber?: string, category?: string, role?: UserRole, teamId?: string, canEditProfile?: boolean } | null> {
  try {
    const userDoc = await getDoc(doc(db, "users", userId));

    if (userDoc.exists()) {
      const data = userDoc.data();
      const registrationNumber = data.registrationNumber || "";
      const fullName = data.fullName || "Utilizador sem nome";

      return {
        id: userDoc.id,
        name: fullName,
        email: data.email,
        registrationNumber: registrationNumber,
        category: data.category,
        role: data.role,
        teamId: data.teamId,
        canEditProfile: data.canEditProfile,
      };
    }

    return null;
  } catch (error) {
    console.error("Erro ao obter utilizador:", error);
    throw error;
  }
}

/**
 * Atualiza o papel de um utilizador
 */
export async function updateUserRole(userId: string, role: UserRole, teamId?: string): Promise<void> {
  try {
    const userDocRef = doc(db, "users", userId);

    // Verificar se o utilizador existe
    const userDoc = await getDoc(userDocRef);
    if (!userDoc.exists()) {
      throw new Error("Utilizador não encontrado");
    }

    const userData = userDoc.data();
    const oldRole = userData.role;
    const oldTeamId = userData.teamId;

    // Preparar dados para atualização
    const updateData: {
      role: UserRole;
      updatedAt: ReturnType<typeof serverTimestamp>;
      teamId?: string | null;
    } = {
      role,
      updatedAt: serverTimestamp(),
    };

    // Adicionar teamId apenas se for necessário
    if (role === UserRole.TEAM_LEADER || role === UserRole.TEAM_MEMBER) {
      if (!teamId) {
        throw new Error("ID da equipa é obrigatório para líderes e membros de equipa");
      }
      updateData.teamId = teamId;
    } else {
      // Se o papel for admin, remover o teamId
      updateData.teamId = null;
    }

    // Atualizar o documento do utilizador
    await setDoc(userDocRef, updateData, { merge: true });

    // Atualizar as equipas

    // 1. Se o utilizador tinha uma equipa anterior, removê-lo dessa equipa
    if (oldTeamId) {
      const oldTeamRef = doc(db, "teams", oldTeamId);
      const oldTeamDoc = await getDoc(oldTeamRef);

      if (oldTeamDoc.exists()) {
        const oldTeamData = oldTeamDoc.data();

        // Remover o utilizador da lista de líderes ou membros, dependendo do papel anterior
        if (oldRole === UserRole.TEAM_LEADER) {
          await setDoc(oldTeamRef, {
            leaders: oldTeamData.leaders.filter((id: string) => id !== userId),
            updatedAt: serverTimestamp()
          }, { merge: true });
        } else if (oldRole === UserRole.TEAM_MEMBER) {
          await setDoc(oldTeamRef, {
            members: oldTeamData.members.filter((id: string) => id !== userId),
            updatedAt: serverTimestamp()
          }, { merge: true });
        }
      }
    }

    // 2. Se o utilizador está a ser adicionado a uma nova equipa, adicioná-lo a essa equipa
    if (teamId) {
      const newTeamRef = doc(db, "teams", teamId);
      const newTeamDoc = await getDoc(newTeamRef);

      if (newTeamDoc.exists()) {
        const newTeamData = newTeamDoc.data();

        // Adicionar o utilizador à lista de líderes ou membros, dependendo do novo papel
        if (role === UserRole.TEAM_LEADER) {
          const leaders = newTeamData.leaders || [];
          if (!leaders.includes(userId)) {
            await setDoc(newTeamRef, {
              leaders: [...leaders, userId],
              updatedAt: serverTimestamp()
            }, { merge: true });
          }
        } else if (role === UserRole.TEAM_MEMBER) {
          const members = newTeamData.members || [];
          if (!members.includes(userId)) {
            await setDoc(newTeamRef, {
              members: [...members, userId],
              updatedAt: serverTimestamp()
            }, { merge: true });
          }
        }
      }
    }
  } catch (error) {
    console.error("Erro ao atualizar papel do utilizador:", error);
    throw error;
  }
}

/**
 * Atualiza o perfil de um utilizador (admin)
 */
export async function updateUserProfile(userId: string, profileData: {
  fullName?: string;
  registrationNumber?: string;
  category?: string;
  email?: string;
  canEditProfile?: boolean;
}): Promise<void> {
  try {
    const userDocRef = doc(db, "users", userId);

    // Verificar se o utilizador existe
    const userDoc = await getDoc(userDocRef);
    if (!userDoc.exists()) {
      throw new Error("Utilizador não encontrado");
    }

    // Preparar dados para atualização
    const updateData: {
      fullName?: string;
      registrationNumber?: string;
      category?: string;
      email?: string;
      canEditProfile?: boolean;
      updatedAt: ReturnType<typeof serverTimestamp>;
    } = {
      ...profileData,
      updatedAt: serverTimestamp(),
    };

    // Atualizar o documento do utilizador
    await setDoc(userDocRef, updateData, { merge: true });

    console.log("Perfil do utilizador atualizado com sucesso:", userId);
  } catch (error) {
    console.error("Erro ao atualizar perfil do utilizador:", error);
    throw error;
  }
}

/**
 * Obtém todos os utilizadores de uma equipa
 */
export async function getTeamUsers(teamId: string): Promise<{ id: string, name: string, email?: string, registrationNumber?: string, role?: UserRole }[]> {
  try {
    // Buscar todos os utilizadores
    const users = await getSystemUsers();

    // Filtrar apenas os utilizadores da equipa
    return users.filter(user => user.teamId === teamId);
  } catch (error) {
    console.error("Erro ao obter utilizadores da equipa:", error);
    throw error;
  }
}

/**
 * Regista um novo utilizador no sistema
 */
export async function registerUser(userData: UserRegisterData): Promise<{ userId: string, resetLink: string }> {
  try {
    console.log('Enviando solicitação para registar utilizador:', { ...userData, password: '***' });

    const response = await fetch('/api/users/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    // Tentar obter o corpo da resposta, mesmo se não for OK
    let responseData;
    try {
      responseData = await response.json();
    } catch (parseError) {
      console.error('Erro ao analisar resposta JSON:', parseError);
      throw new Error('Erro ao processar resposta do servidor');
    }

    if (!response.ok) {
      // Se temos dados de erro, usar a mensagem de erro do servidor
      const errorMessage = responseData?.error || `Erro ao registar utilizador: ${response.status} ${response.statusText}`;
      const errorDetails = responseData?.details ? ` - ${responseData.details}` : '';
      throw new Error(`${errorMessage}${errorDetails}`);
    }

    console.log('Utilizador registado com sucesso:', responseData);
    return {
      userId: responseData.userId,
      resetLink: responseData.resetLink,
    };
  } catch (error) {
    console.error('Erro ao registar utilizador:', error);
    throw error;
  }
}

/**
 * Exclui um utilizador do sistema
 * Nota: Os registos criados pelo utilizador não são afetados
 */
export async function deleteUser(userId: string): Promise<void> {
  try {
    const response = await fetch('/api/users/delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erro ao excluir utilizador');
    }

    console.log('Utilizador excluído com sucesso:', userId);
  } catch (error) {
    console.error('Erro ao excluir utilizador:', error);
    throw error;
  }
}

import { sendPasswordResetEmail as firebaseSendPasswordResetEmail } from "firebase/auth";
import { auth } from "@/lib/firebase";

/**
 * Envia um email de redefinição de password para um utilizador
 * Usa diretamente o Firebase Client SDK para evitar problemas com o Admin SDK em ambiente serverless
 */
export async function sendPasswordResetEmail(email: string): Promise<{ link?: string }> {
  try {
    console.log('Enviando email de redefinição de password para:', email);

    // Configurar a URL de redirecionamento após a redefinição da password
    const actionCodeSettings = {
      url: `${window.location.origin}/login`,
      handleCodeInApp: false,
    };

    console.log('URL de redirecionamento configurada:', actionCodeSettings.url);

    // Usar o Firebase Client SDK para enviar o email diretamente
    await firebaseSendPasswordResetEmail(auth, email, actionCodeSettings);

    console.log('Email de redefinição de password enviado com sucesso');

    // Como estamos usando o Firebase Client SDK, não temos acesso ao link gerado
    // Retornar um objeto vazio para manter a compatibilidade com a interface anterior
    return {};
  } catch (error: unknown) {
    console.error('Erro ao enviar email de redefinição de password:', error);

    // Verificar se o erro é do tipo FirebaseError
    if (typeof error === 'object' && error !== null && 'code' in error) {
      const firebaseError = error as { code: string };

      // Tratar erros específicos do Firebase
      if (firebaseError.code === 'auth/user-not-found') {
        throw new Error('Utilizador não encontrado');
      }

      if (firebaseError.code === 'auth/invalid-email') {
        throw new Error('O email fornecido é inválido');
      }

      if (firebaseError.code === 'auth/invalid-continue-uri') {
        throw new Error('URL de redirecionamento inválido');
      }

      if (firebaseError.code === 'auth/unauthorized-continue-uri') {
        throw new Error('URL de redirecionamento não autorizado');
      }
    }

    // Se não for um erro do Firebase ou não for um dos erros tratados acima
    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error('Erro desconhecido ao enviar email de redefinição de password');
    }
  }
}
