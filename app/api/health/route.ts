import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log("API de health check chamada");
    
    return NextResponse.json({
      success: true,
      message: 'API funcionando corretamente',
      timestamp: new Date().toISOString(),
      environment: {
        nodeEnv: process.env.NODE_ENV,
        gcloudProject: process.env.GCLOUD_PROJECT,
        functionsEmulator: process.env.FUNCTIONS_EMULATOR,
      }
    });
  } catch (error: any) {
    console.error('Erro na API de health check:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro na API de health check',
        details: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
