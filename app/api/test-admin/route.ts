import { NextResponse } from 'next/server';
import { getAdminAuth, getAdminFirestore, getFirebaseAdminStatus } from '@/lib/firebase-admin';

export async function GET() {
  console.log("=== 🧪 API de teste do Firebase Admin chamada ===");

  try {
    // Obter status detalhado da inicialização
    const status = getFirebaseAdminStatus();
    console.log("📊 Status do Firebase Admin:", status);

    // Testar funcionalidades básicas
    console.log("🧪 Testando funcionalidades do Firebase Admin...");

    const testResults: any = {
      status,
      tests: {},
      errors: []
    };

    // Testar Auth
    try {
      console.log("🔐 Testando Auth...");
      const auth = getAdminAuth();
      const authTest = await auth.listUsers(1);
      console.log("✅ Auth funcionando - usuários encontrados:", authTest.users.length);
      testResults.tests.auth = {
        success: true,
        usersFound: authTest.users.length,
        message: `${authTest.users.length} usuários encontrados`
      };
    } catch (authError) {
      console.error("❌ Erro no teste do Auth:", authError);
      testResults.tests.auth = {
        success: false,
        error: authError instanceof Error ? authError.message : 'Erro desconhecido no Auth'
      };
      testResults.errors.push(`Auth: ${authError instanceof Error ? authError.message : 'Erro desconhecido'}`);
    }

    // Testar Firestore
    try {
      console.log("🗄️ Testando Firestore...");
      const firestore = getAdminFirestore();
      const firestoreTest = await firestore.collection('test').limit(1).get();
      console.log("✅ Firestore funcionando - documentos encontrados:", firestoreTest.size);
      testResults.tests.firestore = {
        success: true,
        documentsFound: firestoreTest.size,
        message: `${firestoreTest.size} documentos de teste encontrados`
      };
    } catch (firestoreError) {
      console.error("❌ Erro no teste do Firestore:", firestoreError);
      testResults.tests.firestore = {
        success: false,
        error: firestoreError instanceof Error ? firestoreError.message : 'Erro desconhecido no Firestore'
      };
      testResults.errors.push(`Firestore: ${firestoreError instanceof Error ? firestoreError.message : 'Erro desconhecido'}`);
    }

    // Determinar se o teste geral foi bem-sucedido
    const allTestsSuccessful = testResults.tests.auth?.success && testResults.tests.firestore?.success;

    if (allTestsSuccessful) {
      return NextResponse.json({
        success: true,
        message: 'Firebase Admin SDK está funcionando corretamente! 🎉',
        ...testResults
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: 'Alguns testes do Firebase Admin falharam',
          ...testResults
        },
        { status: 500 }
      );
    }

  } catch (error: unknown) {
    console.error('💥 Erro geral no teste do Firebase Admin:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';

    return NextResponse.json(
      {
        success: false,
        error: 'Erro geral no teste do Firebase Admin',
        details: errorMessage,
        status: getFirebaseAdminStatus()
      },
      { status: 500 }
    );
  }
}
