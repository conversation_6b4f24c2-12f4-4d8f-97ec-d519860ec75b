"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
// import { useRouter } from "next/navigation";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { showLoadingToast } from "@/lib/toast-utils";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { IconEye, IconEyeOff } from "@tabler/icons-react";

const loginSchema = z.object({
  email: z.string().email("Introduza um email válido"),
  password: z.string().min(6, "A palavra-passe deve ter pelo menos 6 caracteres"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const { signIn } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Efeito para garantir que a página esteja pronta para interação
  useEffect(() => {
    // Remover quaisquer classes ou elementos que possam interferir com a interatividade
    if (typeof window !== 'undefined') {
      // Remover a classe que desativa transições
      document.documentElement.classList.remove('no-transitions');

      // Remover a classe que impede o scroll
      document.body.classList.remove('overflow-hidden');

      // Resetar a variável global de logout
      (window as unknown as Record<string, unknown>).__LOGGING_OUT = false;

      // Pequeno atraso para garantir que a página esteja pronta para interação
      setTimeout(() => {
        // Focar no primeiro input para melhorar a usabilidade
        const emailInput = document.querySelector('input[type="email"]');
        if (emailInput) {
          (emailInput as HTMLInputElement).focus();
        }
      }, 500);
    }
  }, []);

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(data: LoginFormValues) {
    setIsLoading(true);
    const loadingToast = showLoadingToast("A iniciar sessão...");

    try {
      console.log("Login page: Iniciando processo de login...");
      await signIn(data.email, data.password, loadingToast);
      console.log("Login page: Login bem-sucedido, aguardando redirecionamento...");
      // O toast de carregamento é dispensado automaticamente no contexto de autenticação
      // Não redirecionamos aqui, pois o redirecionamento é feito no contexto de autenticação
      // com base no estado do perfil do usuário
    } catch (error) {
      console.error("Login page: Erro durante o login:", error);
      // O toast de carregamento é dispensado automaticamente no contexto de autenticação em caso de erro
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Iniciar Sessão</CardTitle>
          <CardDescription>
            Introduza os seus dados para aceder à aplicação
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        type="email"
                        autoComplete="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Palavra-passe</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder="••••••••"
                          type={showPassword ? "text" : "password"}
                          autoComplete="current-password"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <IconEyeOff className="size-4" />
                          ) : (
                            <IconEye className="size-4" />
                          )}
                          <span className="sr-only">
                            {showPassword ? "Ocultar palavra-passe" : "Mostrar palavra-passe"}
                          </span>
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "A iniciar sessão..." : "Iniciar Sessão"}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-muted-foreground">
            Não tem acesso? Contacte o administrador.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
